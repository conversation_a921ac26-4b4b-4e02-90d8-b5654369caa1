# دليل تحديث PHP لحل مشكلة Composer

## المشكلة
```
Fatal error: Composer detected issues in your platform: 
Your Composer dependencies require a PHP version ">= 8.1.0". 
You are running 8.0.30.
```

## الحلول المتاحة

### الحل الأول: تحديث PHP (الموصى به)

#### 1. تحديث PHP على Windows

##### أ) باستخدام XAMPP:
1. قم بتحميل أحدث إصدار من XAMPP من: https://www.apachefriends.org/
2. اختر إصدار يحتوي على PHP 8.1 أو أحدث
3. قم بتثبيت XAMPP الجديد
4. تأكد من تحديث متغير PATH في النظام

##### ب) باستخدام WAMP:
1. قم بتحميل أحدث إصدار من WAMP من: https://www.wampserver.com/
2. اختر إصدار يحتوي على PHP 8.1 أو أحدث
3. قم بتثبيت WAMP الجديد

##### ج) تحديث PHP مباشرة:
1. قم بتحميل PHP 8.1+ من: https://windows.php.net/download/
2. استخرج الملفات إلى مجلد (مثل C:\php81)
3. أضف المجلد إلى متغير PATH
4. انسخ php.ini-development إلى php.ini وقم بتكوينه

#### 2. التحقق من الإصدار الجديد
```bash
php -v
```

#### 3. تحديث Composer
```bash
composer self-update
composer install
```

### الحل الثاني: تجاهل متطلبات المنصة (مؤقت)

#### تشغيل Composer مع تجاهل متطلبات PHP:
```bash
composer install --ignore-platform-reqs
```

#### أو تحديث composer.json:
```json
{
    "config": {
        "platform-check": false
    }
}
```

### الحل الثالث: استخدام Docker

#### إنشاء Dockerfile:
```dockerfile
FROM php:8.1-apache

# تثبيت الإضافات المطلوبة
RUN docker-php-ext-install pdo pdo_mysql

# نسخ الملفات
COPY . /var/www/html/

# تثبيت Composer
RUN curl -sS https://getcomposer.org/installer | php -- --install-dir=/usr/local/bin --filename=composer

# تثبيت التبعيات
RUN composer install
```

### الحل الرابع: استخدام بيئة افتراضية

#### باستخدام Laravel Sail:
```bash
# تثبيت Sail
composer require laravel/sail --dev

# نشر Sail
php artisan sail:install

# تشغيل المشروع
./vendor/bin/sail up
```

## خطوات التحقق بعد التحديث

### 1. التحقق من إصدار PHP:
```bash
php -v
```

### 2. التحقق من إصدار Composer:
```bash
composer --version
```

### 3. تثبيت التبعيات:
```bash
composer install
```

### 4. تشغيل المشروع:
```bash
php artisan serve
```

## نصائح مهمة

1. **احتفظ بنسخة احتياطية** من المشروع قبل التحديث
2. **تأكد من تحديث جميع الإضافات** المطلوبة
3. **اختبر المشروع** بعد التحديث للتأكد من عمله
4. **حدث متغيرات البيئة** إذا لزم الأمر

## إضافات PHP المطلوبة عادة لـ Laravel

```ini
extension=openssl
extension=pdo_mysql
extension=mbstring
extension=tokenizer
extension=xml
extension=ctype
extension=json
extension=bcmath
extension=fileinfo
extension=gd
```

## استكشاف الأخطاء

### إذا استمرت المشكلة:
1. تأكد من أن PHP الجديد في PATH
2. أعد تشغيل Command Prompt
3. تحقق من ملف php.ini
4. تأكد من تفعيل الإضافات المطلوبة

### للتحقق من الإضافات المثبتة:
```bash
php -m
```

### للتحقق من مسار PHP:
```bash
where php
```

## الخلاصة

الحل الأفضل هو تحديث PHP إلى الإصدار 8.1 أو أحدث. هذا يضمن:
- توافق كامل مع Laravel الحديث
- أمان أفضل
- أداء محسن
- دعم للميزات الجديدة

إذا كان التحديث غير ممكن مؤقتاً، يمكن استخدام `--ignore-platform-reqs` كحل مؤقت، لكن هذا قد يسبب مشاكل في التوافق.
